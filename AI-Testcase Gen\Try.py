"""🧠 Test Case Generator Expert with Knowledge - Your AI Test Case Generating Assistant!
 
This example shows how to create an AI test case generating assistant that combines knowledge from a
operating maual given as input with web searching capabilities too. The agent uses a PDF knowledge base
of operating manual of AUT and can supplement this information with web searches when needed.
 
Run `pip install openai lancedb tantivy pypdf duckduckgo-search agno` to install dependencies.
"""
 
'''
Note: search_type parameter needs tantivy to be installed. So, it could be optional for now.
'''
 
 
from textwrap import dedent
 
from agno.agent import Agent
#from agno.embedder.openai import OpenAIEmbedder
from agno.embedder.google import GeminiEmbedder
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.knowledge.pdf import PDFKnowledgeBase
#from agno.models.openai import OpenAIChat
from agno.models.google import Gemini
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb, SearchType
#from agno.playground import Playground, serve_playground_app
 
# Create a Recipe Expert Agent with knowledge of Thai recipes
TestCaseGenAgent = Agent(
    model=Gemini(),
    instructions=dedent("""\
        You are a experienced and knowledgeable Autmation Tester expert! 👩‍💼
        Think of yourself as a combination of a professional, enthusiastic automation developer.
 
        Follow these steps when answering questions:
        1. First, search the knowledge base for knowledge about the application.Then, suggest testing methods that could be used for the application.
        2. If the information in the knowledge base is incomplete OR if the user asks a question better suited for the web, search the web to fill in gaps
        3. If you find the information in the knowledge base, no need to search the web
        4. Always prioritize knowledge base information over web results for accuracy.
        5. If the user asks about test case generation. Get the knowledge of the application and suggest test cases by accessing web for test case creation.
 
        Communication style:
        1. Start each response with a quote quoted by famous philosophers. Make it random and new.
        2. Start each response with any latest news about AI. Make it random and new.
        2. Structure your responses clearly:
            - Brief introduction or context
            - Main content (application usage given, Test case suggested and reason)
            - Use tables to fill in generated test cases.
        4. Use friendly, encouraging language
 
        Special features:
        - Explain unfamiliar Test methods for test case generated
        - Share relevant refrences searched
 
        End each response with an uplifting sign-off like:
        - 'Happy Testing! Sayonara (Enjoy your Test)!'
 
        Remember:
        - Always verify recipe accuracy with the knowledge base
        - Clearly indicate when information comes from web sources\
    """),
    knowledge=PDFKnowledgeBase(
        path=r"C:\Users\<USER>\Downloads\HAMILTON-C1-T1_INTELLiVENT-ASV_ops-manual_v3.0x_en_10098020.01.pdf",
        vector_db=LanceDb(
            uri="tmp/lancedb",
            table_name="testing_knowledge",
            #search_type=SearchType.hybrid,
            embedder=GeminiEmbedder(),
        ),
    ),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
    add_references=True,
)
 
# Comment out after the knowledge base is loaded
# if TestCaseGenAgent.knowledge is not None:
#     TestCaseGenAgent.knowledge.load()
 
# agent.print_response(
#     "Suggest some test methods for this application. Make it in scope of every functionality of the product with the knowledge given.", stream=True
# )
# agent.print_response(
#     "Generate test case for a specfic functionality end-to-end with atleast 10 test steps.The case should only cover functional testing.", stream=True
# )
 
 
if __name__ == "__main__":
    if TestCaseGenAgent.knowledge is not None:
        TestCaseGenAgent.knowledge.load()
    # TestCaseGenAgent.print_response(
    # "Generate test case for a specfic functionality end-to-end with atleast 10 test steps.The case should only cover functional testing.", stream=True
    # )
    TestCaseGenAgent.print_response("Generate test case for a crucial functionality in the device. Give a name to the test case and this case should have atleast 35 test steps in it",stream=True)
    # TestApp = Playground(agents=[TestCaseGenAgent]).get_app()
    # serve_playground_app("Playground:TestApp", reload=True)

# import chardet

# files = [r"C:\Users\<USER>\Downloads\Generic Sample requirements for GenAI Demo.docx" , r"C:\Users\<USER>\Downloads\Generic - Alpha Firmware upgrade.txt"]
              
# for file in files:
#     with open(file, "rb") as f:
#         raw_data = f.read()
#         detected_encoding = chardet.detect(raw_data)["encoding"]
#         # logger.info(f"Detected encoding for {file}: {detected_encoding}")
#         print(f"Detected encoding for {file}: {detected_encoding}")
